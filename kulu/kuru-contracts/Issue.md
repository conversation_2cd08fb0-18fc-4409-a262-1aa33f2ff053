What “confused-deputy” means here

The Router is the powerful “deputy”: it owns the markets it deploys, can whitelist them in MarginAccount (via updateMarkets), and grants token approvals from itself.

deployProxy() is public. So an attacker can make the Router (the deputy) do privileged actions on their request—even though the attacker themselves is not authorized to do those actions directly.

The exact privileged side-effects an attacker can trigger

Calling deployProxy(...) (no auth):

Registers the new market in verifiedMarket[proxy] = MarketParams(...).

Whitelists it at MarginAccount via IMarginAccount(marginAccountAddress).updateMarkets(proxy) (the call is made by the Router, not the attacker).

Grants unlimited ERC-20 allowances from the Router → market for base/quote tokens via _setApprovalsForMarket(..., type(uint256).max).

None of those three steps check who initiated the deployment. The caller can be anyone.

Why this is a valid issue (and not just cosmetic)

Authority misuse (classic confused-deputy):

Only the Router (or governance) should expand the trusted/whitelisted market set and mint allowances from Router.

Today, any EOA can cause the Router to expand that set and mint allowances on their behalf, simply by calling deployProxy.

That’s exactly the “confused-deputy” class: an unprivileged party gets a privileged party to spend its authority based on untrusted input.

Trust inference from “verified” name:

The mapping is called verifiedMarket. UIs, bots, or downstream contracts often treat “verified” as a whitelist / trust signal.

Because registration is permissionless, “verified” is not a security boundary—yet it looks like one. That mismatch regularly leads to real-world routing, listing, or fee-sharing mistakes.

Unlimited approvals amplify blast radius:

Approvals are standing rights. Once set to uint256.max, a (buggy/compromised) market can transferFrom(Router, token, …) at any time the Router holds balances (e.g., during anyToAnySwap, or if tokens ever accumulate on Router).

Permissionless creation → many markets → many approved spenders. If a later upgrade or bug hits any market implementation, the attack surface is wider and the damage is larger.

Whitelisting by side-effect:

Even if MarginAccount.updateMarkets is correctly gated to “only Router,” the attacker still succeeds because Router is the one calling it inside deployProxy.

So the attacker indirectly expands the MarginAccount trusted set without governance consent.

Parameter abuse & ecosystem effects:

The attacker picks fee/tick/precision parameters. If off-chain indexers or auto-routers use verifiedMarket to discover venues, this permits spam markets, predatory fee markets, or griefing routes that look “official.”

Concrete attack sketches (no exotic assumptions)

A1 — Inflate the approved-spender set: attacker calls deployProxy for many (base,quote) pairs. Router now approves dozens/hundreds of markets for USDC/WETH/etc. If a later market upgrade or latent bug enables an abusive transferFrom, Router balances during user swaps can be siphoned.

A2 — Social/UX exploit: front-ends or scripts that rely on verifiedMarket for “official” listings will surface attacker-spawned markets. Routing through those can cause outsized fees or unexpected behavior while still appearing “verified.”

A3 — Governance boundary bypass (indirect): even if governance intended to curate the market list, any EOA can extend it today—because the Router performs the whitelisting call on their trigger.

Note: You can’t upload arbitrary bytecode via deployProxy—it uses the Router’s current implementations. That reduces immediate RCE risk, but it doesn’t remove the problem: the authority to (a) whitelist and (b) grant unlimited approvals is still publicly triggerable, and future upgrades/bugs can turn those standing approvals into real theft.

Severity (when is this High vs. Medium?)

High if:

MarginAccount gates balance mutation on “isMarket,” and updateMarkets called by Router auto-enables the new market; or

Router sometimes holds nontrivial token balances during flows (it does in anyToAnySwap) and market code can ever pull them (now or via future upgrade).

Medium if:

MarginAccount has strong additional checks AND Router never holds balances except in tightly scoped moments, and you promptly eliminate unlimited approvals.

How to fix (minimal, practical)

Separate deploy from verify (and gate verify):

function deployProxy(...) public returns (address) { ... } // allowed
function verifyMarket(address m, MarketParams calldata p) public onlyOwner { 
    verifiedMarket[m] = p;
    _setApprovalsForMarket(...); // move here
    IMarginAccount(marginAccountAddress).updateMarkets(m);
}


Or simply make deployProxy onlyOwner if you don’t need permissionless creation.

Remove uint256.max approvals:

Use pull-by-market with per-trade, per-token scoped amounts, or lazy approvals that set exact required amount and reset to 0 after.

Add revokeApprovals(address market) as an emergency lever.

Rename verifiedMarket → registeredMarket (or similar) to avoid trust leakage, and add an explicit isVerified boolean only governance can toggle.

Rate-limit / cap number of markets and enforce uniqueness per (base,quote,type) to prevent spam.

Quick tests you (or I) can write

T1: Any EOA calls deployProxy → assert verifiedMarket[proxy].pricePrecision > 0 (i.e., “verified”).

T2: After that call, check IERC20(token).allowance(Router, proxy) == type(uint256).max.

T3: If MarginAccount logs an allow-list change on updateMarkets, confirm it fires and the new market is enabled—despite the caller being an arbitrary EOA (the Router made the call).

Bottom line:
This is valid because an untrusted caller can cause the Router to (a) label a market as “verified,” (b) whitelist it with MarginAccount, and (c) grant it unlimited spending power from Router—all privileged actions—without any governance/owner approval. That’s the definition of a confused-deputy hazard, and the unlimited approvals make the eventual impact potentially catastrophic if any market code (now or post-upgrade) can exploit them.